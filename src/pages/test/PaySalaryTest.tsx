import React from 'react';
import { Button, Card, Space, Dropdown, message, MenuProps } from 'antd';
import { useNavigate } from 'react-router-dom';
import { DollarOutlined, BankOutlined, DownOutlined } from '@ant-design/icons';

// 测试用户数据
const testUsers = [
  { code: 'U001', nickname: '张三' },
  { code: 'U002', nickname: '李四' },
  { code: 'U003', nickname: '王五' },
];

const PaySalaryTest: React.FC = () => {
  const navigate = useNavigate();

  // 发薪快捷功能（复制自UserManagement.tsx）
  const handlePaySalary = (user: { code: string; nickname: string }) => {
    // 构建跳转参数
    const params = new URLSearchParams({
      type: 'human_resources',
      employeeInfo: user.nickname,
      humanResourcesAuditStatus: 'approved',
      createDate: new Date().toISOString().split('T')[0], // 今天的日期 YYYY-MM-DD 格式
    });

    // 跳转到运营资产页面
    navigate(`/financial/operating-assets?${params.toString()}`);
  };

  // 设置公司功能（模拟）
  const handleSetCompany = (user: { code: string; nickname: string }, companyCode: string) => {
    const companyNames: { [key: string]: string } = {
      '01': '广州',
      '02': '杭州',
      '03': '金宝',
    };
    message.success(`已将用户 ${user.nickname} 的公司设置为${companyNames[companyCode]}`);
  };

  // 创建设置公司的下拉菜单
  const getCompanyMenuItems = (user: { code: string; nickname: string }): MenuProps['items'] => [
    {
      key: '01',
      label: '广州',
      onClick: () => handleSetCompany(user, '01'),
    },
    {
      key: '02',
      label: '杭州',
      onClick: () => handleSetCompany(user, '02'),
    },
    {
      key: '03',
      label: '金宝',
      onClick: () => handleSetCompany(user, '03'),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card title="用户管理快捷功能测试页面" style={{ maxWidth: 800 }}>
        <p>这是一个测试页面，用于验证发薪和设置公司的快捷功能。</p>

        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          {testUsers.map((user) => (
            <Card key={user.code} size="small" style={{ backgroundColor: '#fafafa' }}>
              <div
                style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
              >
                <div>
                  <strong>用户编码:</strong> {user.code} <br />
                  <strong>用户昵称:</strong> {user.nickname}
                </div>
                <Space>
                  <Button
                    type="primary"
                    icon={<DollarOutlined />}
                    onClick={() => handlePaySalary(user)}
                    style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                  >
                    发薪
                  </Button>
                  <Dropdown
                    menu={{ items: getCompanyMenuItems(user) }}
                    trigger={['click']}
                    placement="bottomLeft"
                  >
                    <Button
                      type="primary"
                      icon={<BankOutlined />}
                      style={{ backgroundColor: '#1890ff', borderColor: '#1890ff' }}
                    >
                      设置公司 <DownOutlined />
                    </Button>
                  </Dropdown>
                </Space>
              </div>
            </Card>
          ))}
        </Space>

        <div
          style={{
            marginTop: '24px',
            padding: '16px',
            backgroundColor: '#f0f8ff',
            borderRadius: '6px',
          }}
        >
          <h4>测试说明：</h4>
          <h5>发薪功能：</h5>
          <ul>
            <li>点击任意用户的"发薪"按钮</li>
            <li>系统会跳转到运营资产页面</li>
            <li>自动打开新增表单并预填充以下信息：</li>
            <ul>
              <li>资产类型：人力资产</li>
              <li>员工信息：用户昵称</li>
              <li>审核状态：已审核</li>
              <li>创建日期：今天</li>
            </ul>
            <li>只需要手动填写金额、上传截图和备注即可</li>
          </ul>
          <h5>设置公司功能：</h5>
          <ul>
            <li>点击任意用户的"设置公司"下拉按钮</li>
            <li>选择公司：01-广州、02-杭州、03-金宝</li>
            <li>系统会更新用户的公司编码字段</li>
            <li>显示成功消息提示</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default PaySalaryTest;
