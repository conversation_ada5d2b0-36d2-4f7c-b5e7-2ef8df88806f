import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Modal,
  Form,
  Popconfirm,
  Row,
  Col,
  DatePicker,
  Image,
  Tag,
  Statistic,
  Upload,
  Tooltip,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  FileImageOutlined,
  FileExcelOutlined,
  DownloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import type { TableRowSelection } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import {
  getExpenseList,
  createExpenseDetail,
  updateExpenseDetail,
  deleteExpenseDetail,
  getExpenseDetail,
  exportExpenseExcel,
  downloadExpenseImportTemplate,
  importExpenseExcel,
} from '@/api/ExpenseApi';
import type { AddExpenseParams, Detail } from '@/types/Expense';
import { SortBy, SortOrder } from '@/types/commonFinancial';
import { SimpleImageUpload } from '@/components/ImageUpload';
import { SupplierSearchSelector } from '@/components';
import { formatAmountSimple, formatNumber } from '@/utils/formatUtils';
import FormattedAmountInput from '@/components/FormattedAmountInput';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './FixedAssetsManagement.module.css';

const { RangePicker } = DatePicker;
const { TextArea } = Input;

interface ExpenseFormData extends Omit<AddExpenseParams, 'createDate'> {
  createDate: dayjs.Dayjs | string;
}

const ExpenseAssetsManagement: React.FC = () => {
  const [form] = Form.useForm<ExpenseFormData>();
  const [expenses, setExpenses] = useState<Detail[]>([]);
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingExpense, setEditingExpense] = useState<Detail | null>(null);
  const [supplierSearch, setSupplierSearch] = useState('');
  const [remarkSearch, setRemarkSearch] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().startOf('year'),
    dayjs(),
  ]);
  // 排序状态 - 初始不排序，让后端使用默认排序
  const [sortBy, setSortBy] = useState<SortBy | undefined>(undefined);
  const [sortOrder, setSortOrder] = useState<SortOrder | undefined>(undefined);

  // 获取支出列表
  const fetchExpenses = async (
    page = 1,
    pageSize = 10,
    startTime = '',
    endTime = '',
    supplierSearch = '',
    remarkSearch = '',
    currentSortBy = sortBy,
    currentSortOrder = sortOrder,
  ) => {
    setLoading(true);
    try {
      const params: any = {
        page,
        pageSize,
        startTime,
        endTime,
        supplierSearch: supplierSearch.trim() || undefined,
        remarkSearch: remarkSearch.trim() || undefined,
      };

      // 只有在有排序参数时才传递
      if (currentSortBy && currentSortOrder) {
        params.sortBy = currentSortBy;
        params.sortOrder = currentSortOrder;
      }

      const response = await getExpenseList(params);

      if (response.code === 200) {
        setExpenses(response.data.details);
        setTotalAmount(response.data.totalAmount);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || '获取支出列表失败');
      }
    } catch (error: any) {
      logError('获取支出列表', error);
      message.error(getErrorMessage(error, '获取支出列表失败'));
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    const [startTime, endTime] = dateRange;
    fetchExpenses(
      1,
      10,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      '',
      '',
      sortBy,
      sortOrder,
    );
  }, []);

  // 搜索处理
  const handleSearch = () => {
    setPagination((prev) => ({ ...prev, current: 1 }));
    const [startTime, endTime] = dateRange;
    fetchExpenses(
      1,
      pagination.pageSize,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      supplierSearch,
      remarkSearch,
      sortBy,
      sortOrder,
    );
  };

  // 日期范围变化处理
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setDateRange(dates);
      setPagination((prev) => ({ ...prev, current: 1 }));
      fetchExpenses(
        1,
        pagination.pageSize,
        dates[0].format('YYYY-MM-DD'),
        dates[1].format('YYYY-MM-DD'),
        supplierSearch,
        remarkSearch,
        sortBy,
        sortOrder,
      );
    }
  };

  // 分页和排序处理
  const handleTableChange = (paginationConfig: TablePaginationConfig, _: any, sorter: any) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    setPagination((prev) => ({ ...prev, current, pageSize }));

    // 处理排序
    let newSortBy: SortBy | undefined = sortBy;
    let newSortOrder: SortOrder | undefined = sortOrder;

    if (sorter && sorter.field) {
      // 根据排序字段映射到后端字段
      if (sorter.field === 'amount') {
        newSortBy = SortBy.AMOUNT;
      } else if (sorter.field === 'createDate') {
        newSortBy = SortBy.CREATE_DATE;
      }

      // 处理排序方向，包括取消排序的情况
      if (sorter.order === 'ascend') {
        newSortOrder = SortOrder.ASC;
      } else if (sorter.order === 'descend') {
        newSortOrder = SortOrder.DESC;
      } else {
        // 取消排序时，清空排序参数
        newSortBy = undefined;
        newSortOrder = undefined;
      }

      // 更新排序状态
      setSortBy(newSortBy);
      setSortOrder(newSortOrder);
    }

    const [startTime, endTime] = dateRange;
    fetchExpenses(
      current,
      pageSize,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      supplierSearch,
      remarkSearch,
      newSortBy,
      newSortOrder,
    );
  };

  // 刷新列表
  const handleRefresh = () => {
    const [startTime, endTime] = dateRange;
    fetchExpenses(
      pagination.current,
      pagination.pageSize,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      supplierSearch,
      remarkSearch,
      sortBy,
      sortOrder,
    );
  };

  // 新增支出
  const handleAdd = () => {
    setEditingExpense(null);
    form.resetFields();
    // 设置默认的创建日期为当天
    form.setFieldsValue({
      createDate: dayjs(),
    });
    setModalVisible(true);
  };

  // 编辑支出
  const handleEdit = async (record: Detail) => {
    try {
      const response = await getExpenseDetail(record.id);
      if (response.code === 200) {
        setEditingExpense(record);
        form.setFieldsValue({
          supplierCode: response.data.supplierCode,
          amount: response.data.amount,
          screenshot: response.data.screenshot,
          remark: response.data.remark,
          createDate: response.data.createDate ? dayjs(response.data.createDate) : dayjs(),
        });
        setModalVisible(true);
      } else {
        message.error(response.message || '获取支出详情失败');
      }
    } catch (error: any) {
      logError('获取支出详情', error);
      message.error(getErrorMessage(error, '获取支出详情失败'));
    }
  };

  // 删除支出
  const handleDelete = async (id: string) => {
    try {
      const response = await deleteExpenseDetail(id);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('删除支出', error);
      message.error(getErrorMessage(error, '删除失败'));
    }
  };

  // 保存支出
  const handleSave = async () => {
    if (saveLoading) return; // 防止重复提交

    setSaveLoading(true);
    try {
      const values = await form.validateFields();
      // 格式化数据
      const formattedValues = {
        ...values,
        amount: String(values.amount || 0), // 转换为字符串
        createDate: values.createDate
          ? typeof values.createDate === 'string'
            ? values.createDate
            : values.createDate.format('YYYY-MM-DD')
          : dayjs().format('YYYY-MM-DD'),
      };
      let response;

      if (editingExpense) {
        // 编辑模式
        response = await updateExpenseDetail(editingExpense.id, formattedValues);
      } else {
        // 新增模式
        response = await createExpenseDetail(formattedValues);
      }

      const result = handleApiResponse(
        response,
        editingExpense ? '更新成功' : '创建成功',
        editingExpense ? '更新失败' : '创建失败',
      );

      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError(editingExpense ? '更新支出' : '创建支出', error);
      message.error(getErrorMessage(error, editingExpense ? '更新失败' : '创建失败'));
    } finally {
      setSaveLoading(false);
    }
  };

  // 快捷键处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // F2 - 新增
      if (event.key === 'F2' && !modalVisible) {
        event.preventDefault();
        handleAdd();
      }

      // F5 - 搜索
      if (event.key === 'F5') {
        event.preventDefault();
        if (modalVisible) {
          // 如果模态框打开，不处理
          return;
        }
        handleSearch();
        message.info('检索完毕 (F5)');
      }

      // F8 - 保存 (仅在模态框打开时)
      if (event.key === 'F8') {
        event.preventDefault();
        if (modalVisible && !saveLoading) {
          handleSave();
          message.info('正在保存... (F8)');
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [modalVisible, saveLoading, handleSearch, handleSave]);

  // 导出Excel（选中的记录）
  const handleExport = async () => {
    try {
      const [startTime, endTime] = dateRange;
      const params = {
        startTime: startTime.format('YYYY-MM-DD'),
        endTime: endTime.format('YYYY-MM-DD'),
        detailIds: selectedRowKeys.length > 0 ? (selectedRowKeys as string[]) : undefined,
        supplierSearch: supplierSearch.trim() || undefined,
        remarkSearch: remarkSearch.trim() || undefined,
      };

      const blob = await exportExpenseExcel(params);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      const fileName =
        selectedRowKeys.length > 0
          ? `支出资产_选中记录_${startTime.format('YYYY-MM-DD')}_${endTime.format('YYYY-MM-DD')}.xlsx`
          : `支出资产_${startTime.format('YYYY-MM-DD')}_${endTime.format('YYYY-MM-DD')}.xlsx`;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error: any) {
      logError('导出支出Excel', error);
      message.error(getErrorMessage(error, '导出失败'));
    }
  };

  // 导出全部记录
  const handleExportAll = async () => {
    try {
      const [startTime, endTime] = dateRange;
      const params = {
        startTime: startTime.format('YYYY-MM-DD'),
        endTime: endTime.format('YYYY-MM-DD'),
        // 不传detailIds表示导出全部
        supplierSearch: supplierSearch.trim() || undefined,
        remarkSearch: remarkSearch.trim() || undefined,
      };

      const blob = await exportExpenseExcel(params);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `支出资产_全部记录_${startTime.format('YYYY-MM-DD')}_${endTime.format('YYYY-MM-DD')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error: any) {
      logError('导出全部支出Excel', error);
      message.error(getErrorMessage(error, '导出失败'));
    }
  };

  // 下载导入模板
  const handleDownloadTemplate = async () => {
    try {
      const blob = await downloadExpenseImportTemplate();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = '支出明细导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('模板下载成功');
    } catch (error: any) {
      logError('下载导入模板', error);
      message.error(getErrorMessage(error, '下载模板失败'));
    }
  };

  // 导入Excel
  const handleImport = (file: File) => {
    // 验证文件类型
    const isExcel =
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel' ||
      file.name.endsWith('.xlsx') ||
      file.name.endsWith('.xls');

    if (!isExcel) {
      message.error('只能上传Excel文件(.xlsx, .xls)');
      return false;
    }

    // 验证文件大小 (限制为10MB)
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB');
      return false;
    }

    const importFile = async () => {
      const hide = message.loading('正在导入，请稍候...', 0);
      try {
        const response = await importExpenseExcel(file);
        const result = handleApiResponse(response, '导入成功', '导入失败');

        if (result.success) {
          message.success(result.message);
          handleRefresh(); // 刷新列表
        } else {
          message.error(result.message);
        }
      } catch (error: any) {
        logError('导入支出', error);
        message.error(getErrorMessage(error, '导入失败'));
      } finally {
        hide();
      }
    };

    importFile();
    return false; // 阻止默认上传行为
  };

  // 表格行选择
  const rowSelection: TableRowSelection<Detail> = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    getCheckboxProps: (record: Detail) => ({
      disabled: record.isDeleted,
    }),
  };

  // 表格列定义
  const columns: ColumnsType<Detail> = [
    {
      title: '供应商',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 130,
      render: (supplierName: string, record: Detail) => (
        <div>
          <div style={{ fontWeight: 500, fontSize: '13px' }}>{supplierName}</div>
          <div style={{ fontSize: '11px', color: '#8c8c8c' }}>{record.supplierCode}</div>
        </div>
      ),
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 100,
      render: (amount: number) => (
        <Tag color="red" style={{ fontSize: '12px' }}>
          {formatAmountSimple(amount)}
        </Tag>
      ),
      sorter: true,
      sortOrder:
        sortBy === SortBy.AMOUNT && sortOrder
          ? sortOrder === SortOrder.ASC
            ? 'ascend'
            : 'descend'
          : null,
    },
    {
      title: '截图',
      dataIndex: 'screenshot',
      key: 'screenshot',
      width: 80,
      render: (screenshot: string) =>
        screenshot ? (
          <Image
            width={40}
            height={40}
            src={screenshot}
            style={{ objectFit: 'cover', borderRadius: 4 }}
            placeholder={<FileImageOutlined style={{ fontSize: 16, color: '#ccc' }} />}
          />
        ) : (
          <div
            style={{
              width: 40,
              height: 40,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: '#f5f5f5',
              borderRadius: 4,
            }}
          >
            <FileImageOutlined style={{ fontSize: 16, color: '#ccc' }} />
          </div>
        ),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      render: (remark: string) => <span style={{ fontSize: '13px' }}>{remark || '-'}</span>,
    },
    {
      title: '创建日期',
      dataIndex: 'createDate',
      key: 'createDate',
      width: 120,
      render: (createDate: string) => <span style={{ fontSize: '13px' }}>{createDate || '-'}</span>,
      sorter: true,
      sortOrder:
        sortBy === SortBy.CREATE_DATE && sortOrder
          ? sortOrder === SortOrder.ASC
            ? 'ascend'
            : 'descend'
          : null,
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            style={{ fontSize: '12px', padding: '0 4px' }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条支出记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              style={{ fontSize: '12px', padding: '0 4px' }}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 快捷键处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // F5 - 搜索
      if (event.key === 'F5') {
        event.preventDefault();
        if (modalVisible) {
          // 如果模态框打开，不处理
          return;
        }
        handleSearch();
        message.info('检索完毕 (F5)');
      }

      // F8 - 保存 (仅在模态框打开时)
      if (event.key === 'F8') {
        event.preventDefault();
        if (modalVisible && !saveLoading) {
          handleSave();
          message.info('正在保存... (F8)');
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [modalVisible, saveLoading, supplierSearch, remarkSearch, dateRange, pagination]);

  return (
    <div className={styles.container}>
      <Card>
        {/* 统计信息区域 */}
        <div className={styles.statsSection}>
          <Row gutter={[16, 8]} align="middle">
            <Col xs={24} sm={12} lg={8}>
              <div className={styles.statCard}>
                <Statistic
                  title="总支出金额"
                  value={totalAmount}
                  precision={2}
                  prefix="¥"
                  valueStyle={{ color: '#ff4d4f', fontSize: '22px', fontWeight: 'bold' }}
                  formatter={(value) => formatNumber(value as number, 2)}
                />
              </div>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <div className={styles.dateSection}>
                <div className={styles.sectionLabel}>日期范围</div>
                <RangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  style={{ width: '100%' }}
                />
              </div>
            </Col>
            <Col xs={24} sm={24} lg={8}>
              <div className={styles.actionSection}>
                <Space wrap size="small">
                  <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                    新增支出(F2)
                  </Button>
                  <Tooltip title="下载支出明细导入模板">
                    <Button
                      type="default"
                      icon={<DownloadOutlined />}
                      onClick={handleDownloadTemplate}
                    >
                      下载模板
                    </Button>
                  </Tooltip>
                  <Tooltip title="导入Excel文件批量添加支出明细">
                    <Upload accept=".xlsx,.xls" showUploadList={false} beforeUpload={handleImport}>
                      <Button type="default" icon={<UploadOutlined />}>
                        导入
                      </Button>
                    </Upload>
                  </Tooltip>
                  <Button icon={<FileExcelOutlined />} onClick={handleExport} disabled={loading}>
                    {selectedRowKeys.length > 0 ? '导出选中' : '导出当前'}
                  </Button>
                  <Button icon={<FileExcelOutlined />} onClick={handleExportAll} disabled={loading}>
                    导出全部
                  </Button>
                  <Button icon={<ReloadOutlined />} onClick={handleRefresh} disabled={loading}>
                    刷新
                  </Button>
                </Space>
              </div>
            </Col>
          </Row>
        </div>

        {/* 搜索筛选区域 */}
        <div className={styles.searchSection}>
          <div className={styles.searchHeader}>
            <SearchOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            <span className={styles.searchTitle}>筛选条件</span>
          </div>
          <div className={styles.searchContent}>
            <Row gutter={[16, 12]} align="middle" justify="center">
              <Col xs={24} sm={12} lg={7}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>供应商</label>
                  <div className={styles.searchInputWrapper}>
                    <SupplierSearchSelector
                      value={supplierSearch}
                      onChange={(value) => setSupplierSearch(value)}
                      placeholder="请选择供应商"
                      allowClear
                      style={{ width: '100%' }}
                    />
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} lg={7}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>备注</label>
                  <div className={styles.searchInputWrapper}>
                    <Input
                      placeholder="请输入备注关键词"
                      value={remarkSearch}
                      onChange={(e) => setRemarkSearch(e.target.value)}
                      allowClear
                    />
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={24} lg={10}>
                <div className={styles.searchActions}>
                  <Space size="middle" wrap>
                    <Button type="primary" onClick={handleSearch} loading={loading} title="搜索">
                      搜索 (F5)
                    </Button>
                    <Button
                      onClick={() => {
                        setSupplierSearch('');
                        setRemarkSearch('');
                        handleSearch();
                      }}
                    >
                      重置
                    </Button>
                  </Space>
                </div>
              </Col>
            </Row>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={expenses}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            size: 'small',
          }}
          onChange={handleTableChange}
          scroll={{ x: 800 }}
          size="small"
        />
      </Card>

      {/* 支出编辑模态框 */}
      <Modal
        title={editingExpense ? '编辑支出' : '新增支出'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={550}
        destroyOnClose
        confirmLoading={saveLoading}
        okText={`保存 (F8)`}
        cancelText="取消"
      >
        <Form form={form} layout="vertical" preserve={false} size="small">
          <Row gutter={12}>
            <Col span={12}>
              <Form.Item
                name="supplierCode"
                label="供应商"
                rules={[{ required: true, message: '请选择供应商' }]}
              >
                <SupplierSearchSelector placeholder="请选择供应商" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="amount"
                label="金额"
                rules={[
                  { required: true, message: '请输入金额' },
                  { type: 'number', min: 0, message: '金额不能小于0' },
                ]}
              >
                <FormattedAmountInput placeholder="请输入金额" size="small" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={12}>
            <Col span={12}>
              <Form.Item
                name="createDate"
                label="创建日期"
                rules={[{ required: true, message: '请选择创建日期' }]}
              >
                <DatePicker
                  placeholder="请选择创建日期"
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="screenshot" label="截图">
                <SimpleImageUpload folder="expense-assets" enablePaste={true} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={12}>
            <Col span={24}>
              <Form.Item name="remark" label="备注">
                <TextArea placeholder="请输入备注" rows={2} maxLength={500} showCount />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default ExpenseAssetsManagement;
