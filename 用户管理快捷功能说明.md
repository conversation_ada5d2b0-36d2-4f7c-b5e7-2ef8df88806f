# 用户管理快捷功能说明

## 功能概述
在用户管理页面为每个用户添加了两个快捷功能按钮：
1. **发薪按钮**: 快速创建工资支出记录
2. **设置公司按钮**: 快速设置用户的公司管理员权限

## 功能特点

### 1. 发薪快捷功能
- **一键发薪**: 点击绿色发薪按钮即可快速创建工资支出记录
- **自动预填充**: 自动填充以下信息：
  - 资产类型：人力资产
  - 员工信息：使用用户的昵称
  - 审核状态：已审核
  - 创建日期：当天日期
- **绿色按钮**: 使用绿色样式和美元符号图标，易于识别

### 2. 设置公司快捷功能
- **下拉选择**: 点击蓝色设置公司按钮显示下拉菜单
- **三个公司选项**:
  - 01 - 广州
  - 02 - 杭州
  - 03 - 金宝
- **一键设置**: 选择公司后自动设置用户为该公司的管理员
- **蓝色按钮**: 使用蓝色样式和银行图标，易于识别

## 使用步骤

### 发薪功能
1. 进入系统管理 → 用户管理页面
2. 在用户列表的操作列中找到"发薪"按钮（绿色，带有美元符号图标）
3. 点击对应用户的"发薪"按钮
4. 系统自动跳转到运营资产页面，并打开新增表单
5. 表单已预填充基本信息，只需要：
   - 输入金额
   - 上传截图
   - 填写备注（可选）
6. 点击保存完成发薪记录

### 设置公司功能
1. 进入系统管理 → 用户管理页面
2. 在用户列表的操作列中找到"设置公司"按钮（蓝色，带有银行图标）
3. 点击"设置公司"按钮显示下拉菜单
4. 选择目标公司：
   - 广州（编码：01）
   - 杭州（编码：02）
   - 金宝（编码：03）
5. 系统自动设置用户为选定公司的管理员
6. 显示成功消息提示

## 技术实现

### 发薪功能
- 在UserManagement.tsx中添加了handlePaySalary函数
- 使用URL参数传递预填充数据
- 在OperatingAssetsManagement.tsx中添加了URL参数处理逻辑
- 自动打开新增模态框并预填充表单数据

### 设置公司功能
- 在UserManagement.tsx中添加了handleSetCompany函数
- 使用setCompanyAdmin API设置公司管理员权限
- 使用Antd Dropdown组件创建下拉菜单
- 添加了getCompanyMenuItems函数生成菜单项

## 修改的文件

### 1. `src/pages/system/UserManagement.tsx`
- 添加Dropdown、MenuProps、BankOutlined、DownOutlined导入
- 添加setCompanyAdmin API导入
- 添加handlePaySalary函数（发薪功能）
- 添加handleSetCompany函数（设置公司功能）
- 添加getCompanyName函数（获取公司名称）
- 添加getCompanyMenuItems函数（生成下拉菜单）
- 在操作列中添加发薪按钮和设置公司下拉按钮
- 调整操作列宽度从320px到400px

### 2. `src/pages/financial/OperatingAssetsManagement.tsx`
- 添加useSearchParams导入
- 添加URL参数处理逻辑
- 自动打开表单并预填充数据

### 3. `src/pages/test/PaySalaryTest.tsx`（测试页面）
- 创建测试页面验证两个功能
- 包含模拟的设置公司功能

## 公司编码映射
- **01**: 广州
- **02**: 杭州  
- **03**: 金宝

## 注意事项
- 发薪按钮对所有用户都可见，包括超级管理员
- 设置公司按钮对所有用户都可见，包括超级管理员
- 预设审核状态为"已审核"，符合发薪的业务逻辑
- 员工信息字段使用用户昵称，可根据需要手动修改
- 金额和截图字段仍需手动填写
- 设置公司功能会将用户设置为对应公司的管理员
- 操作成功后会自动刷新用户列表显示最新状态

## 测试方法
访问测试页面：`http://localhost:5173/test/pay-salary`
可以测试两个功能的UI交互和基本逻辑。
